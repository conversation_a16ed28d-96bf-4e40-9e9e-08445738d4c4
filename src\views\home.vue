<template>
  <div class="dashboard-container">
    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-item">
        <label>显示层级：</label>
        <el-slider
          v-model="treeDepth"
          :min="1"
          :max="5"
          :step="1"
          show-stops
          show-tooltip
          style="width: 150px; margin-left: 10px;"
          @change="updateTreeDepth"
        />
        <span style="margin-left: 10px;">{{ treeDepth }} 层</span>
      </div>
    </div>
    <div class="echart-container">
      <div id="chart-panel" :style="{ height: viewportHeight, width: width }" />
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import echarts from 'echarts'
import { queryDeviceSwitch } from '@/api/fhm/deviceShowApi'

require('echarts/theme/macarons') // echarts theme
import request from '@/utils/request'
import fhm from './dashboard/fhm'
import resize from './dashboard/mixins/resize'

export default {
  name: 'Dashboard',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    fhm

  },
  mixins: [resize],
  data() {
    return {
      arrDevice: [],
      viewportHeight: window.innerHeight, // 初始设置为当前窗口高度
      width: '100%',
      height: '800px',
      treeDepth: 2, // 默认显示2层
      chart: null, // 存储图表实例
      // 颜色
      colors: [
        '#3CB371'
      ]
    }
  },
  mounted() {
    this.updateViewportHeight()
    window.addEventListener('resize', this.updateViewportHeight)
    this.$nextTick(() => {
      this.getData()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateViewportHeight)
  },
  methods: {

    updateViewportHeight() {
      this.viewportHeight = window.innerHeight - 100 + 'px'
    },
    /*      getData() {
           const params = {};
           const res =  queryDeviceSwitch(params);
           let arr = [];
           arr.push(res)
           arr = this.handleData(arr, 0)
           this.arrDevice = arr;

           return arr;
         },*/

    getData() {
      const params = {

      }
      let arr = []
      // 使用request调用API，但不显示loading状态
      request({
        url: 'api/deviceApi/queryDeviceSwitch',
        method: 'get',
        params: params
      }).then(res => {
        arr.push(res)
        arr = this.handleData(arr, 0)

        this.arrDevice = arr
        this.initChart(arr)
      }).catch(error => {
        console.error('静默获取历史数据失败:', error)
        // 静默失败，不显示错误消息
      })
      return arr
    },

    // 获取所有数据
    /*   getData() {
            const data = {
              name: '升压站',
              value: "1",
              id: 0,
              children: []
            }
            for (let i = 1; i <= 19; i++) {
              const obj = {
                name: i + '#箱变',
                value: i+"swewe",
                id: 0,
                children: []
              }
              for (let j = 1; j <= 8; j++) {
                const obj2 = {
                  id: 0,
                  name: `逆变器-${i}-${j}`,
                  value: 1 + '-' + i + '-' + j
                }
                // eslint-disable-next-line eqeqeq
                if (j % 2 == 1) {
                  obj2.children = []
                  for (let k = 1; k <= 2; k++) {
                    const obj3 = {
                      id: 0,
                      name: `组串-${i}-${j}-${k}`,
                      value: 1 + '-' + i + '-' + j + '-' + k
                    }
                    obj2.children.push(obj3)
                  }
                }

                obj.children.push(obj2)
              }

              data.children.push(obj)
            }
            let arr = []
            arr.push(data)
            console.log(JSON.stringify(arr))

            arr = this.handleData(arr, 0)

            return arr
          },*/

    // 数据颜色  组装
    handleData(data, index, color = '#3CB371') {
      // index标识第几层
      return data.map((item, index2) => {
        // 计算出颜色
        // eslint-disable-next-line eqeqeq
        if (index == 1) {
          // eslint-disable-next-line no-undef,eqeqeq
          //  color = this.colors.find((item, eq) => eq == index2 % 5)
          color = '#3CB371'
        }
        // 设置节点大小
        if (index === 0 || index === 1) {
          item.label = {
            position: 'inside'
            //   rotate: 0,
            //   borderRadius: "50%",
          }
        }
        // 设置label大小 - 矩形布局使用[宽, 高]格式
        switch (index) {
          case 0:
            item.symbolSize = [120, 50] // 根节点使用较大的矩形
            item.itemStyle = {
              color: '#00a6fb',
              borderWidth: 2,
              borderColor: '#fff'
            }
            item.label = {
              fontSize: 14,
              fontWeight: 'bold'
            }
            break
          case 1:
            item.symbolSize = [100, 35] // 二级节点
            item.itemStyle = {
              color: '#0582ca',
              borderWidth: 1,
              borderColor: '#fff'
            }
            item.label = {
              fontSize: 12
            }
            break
          default:
            item.symbolSize = [80, 25] // 叶子节点
            item.itemStyle = {
              color: '#006494',
              borderWidth: 1,
              borderColor: '#fff'
            }
            item.label = {
              fontSize: 10
            }
            break
        }
        // 设置线条颜色
        item.lineStyle = {
          color: color
        }

        // 根据传入的颜色参数设置当前节点的颜色
        if (color === 'red') {
          item.itemStyle = {
            color: 'red',
            borderColor: 'red'
          }
        } else {
          item.itemStyle = {
            borderColor: '#3CB371',
            color: '#3CB371'
          }
        }

        if (item.children) { // 存在子节点
          if (index2 === 1) {
            // 当index2为1时，只将子节点设置为红色，当前节点保持原色
            item.children = this.handleData(item.children, index + 1, 'red')
          } else {
            item.children = this.handleData(item.children, index + 1, color)
          }
        }
        return item
      })
    },

    initChart(data) {
      // this.chart = echarts.init(this.$el, 'macarons')
      var dom = document.getElementById('chart-panel')
      this.chart = echarts.init(dom)
      const option = {
        type: 'tree',
        backgroundColor: '#000',
        toolbox: { // 工具栏
          show: true,
          iconStyle: {
            borderColor: '#03ceda'
          },
          feature: {
            restore: {}
          }
        },
        tooltip: { // 提示框
          trigger: 'item',
          triggerOn: 'mousemove',
          backgroundColor: 'rgba(1,70,86,1)',
          borderColor: 'rgba(0,246,255,1)',
          borderWidth: 0.5,
          textStyle: {
            fontSize: 10
          }
        },
        /*
                  symbol: 'rect', // 节点标记形状
                  edgeShape: 'polyline', //设置连接线曲线还是折线，默认情况下是曲线，curve曲线 polyline直线
                  orient: 'vertical', //树整体的方向horizontal横向 vertical竖向*/
        series: [{
          type: 'tree',
          hoverAnimation: true, // hover样式
          data: data,
          top: 50,
          bottom: 50,
          left: 50,
          right: 50,
          layout: 'orthogonal', // 改为正交布局（直角布局）
          orient: 'TB', // 从上到下的方向
          symbol: 'rect', // 改为矩形节点
          symbolSize: [60, 25], // 增大节点尺寸
          nodePadding: 50, // 增加节点间距
          layerPadding: 80, // 增加层级间距
          animationDurationUpdate: 750,
          expandAndCollapse: true, // 子树折叠和展开的交互，默认打开
          initialTreeDepth: this.treeDepth, // 使用动态层级深度
          roam: true, // 是否开启鼠标缩放和平移漫游
          focusNodeAdjacency: true,
          itemStyle: {
            borderWidth: 1,
            borderRadius: 4 // 添加圆角让矩形更美观
          },
          label: { // 标签样式
            color: '#fff',
            fontSize: 11, // 增大字体
            fontFamily: 'SourceHanSansCN',
            fontWeight: 'normal',
            overflow: 'truncate', // 文字溢出时截断
            width: 55 // 限制标签宽度
          },
          lineStyle: {
            width: 2, // 增加连接线宽度
            curveness: 0 // 设置为0，使连接线为直线
          },
          emphasis: { // 鼠标悬停时的样式
            focus: 'descendant',
            itemStyle: {
              borderWidth: 2,
              shadowBlur: 10,
              shadowColor: '#00f6ff'
            },
            label: {
              fontSize: 12,
              fontWeight: 'bold'
            }
          }
        }]
      }
      this.chart.setOption(option)
      /* if (option && typeof option === 'object') {
         myChart.setOption(option)
       }*/
    },

    // 更新树的显示层级
    updateTreeDepth() {
      if (this.chart) {
        const option = this.chart.getOption()
        option.series[0].initialTreeDepth = this.treeDepth
        this.chart.setOption(option, true) // true表示重新渲染
      }
    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 8px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  .control-item {
    display: flex;
    align-items: center;

    label {
      font-size: 14px;
      color: #fff;
      white-space: nowrap;
    }
  }
}

.dashboard-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.echart-container {
  width: 100%;
  height: 100%;
}
</style>
